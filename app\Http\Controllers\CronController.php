<?php

namespace App\Http\Controllers;

use App\Booking;
use App\CommonSetting;
use App\Http\Controllers\Controller;
use App\Listing;
use App\Models\CurrencyConversionRate;
use App\Models\User;
use App\Services\{BookingService, CancellationPolicyService, ListingService};
use App\Wallet;
use App\WithdrawalRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CronController extends Controller
{
    private $clientId;
    private $clientSecret;
    function __construct(
        protected CancellationPolicyService $cancellationPolicyService,
        protected BookingService $bookingService,
        protected ListingService $listingService
    ) {
        $this->clientId = "tipalti.thirdpartyapi.FTeCrli-8e3rUbeQTMrzBh4FxKg";
        $this->clientSecret = "aSTyuU6SY0rjOzx0EcVzOYqyW_0";
    }
    function index()
    {
        $bookings = Booking::where("status", 0)->get();
        $completedBookings = 0;

        foreach ($bookings as $booking) {
            if ($booking->check_out) {
                $check_out = Carbon::parse($booking->check_out);

                // For Daily bookings, mark as completed if check_out date has passed
                if (in_array($booking->listing_basis, ["Daily", "Tour"]) && $check_out <= today()) {
                    $booking->status = 3; // Completed
                    $booking->save();
                    $completedBookings++;
                }

                // For Hourly bookings, check if all slots for the day have passed
                elseif ($booking->listing_basis == "Hourly") {
                    $today = Carbon::now()->format('Y-m-d');
                    $currentTime = Carbon::now();

                    // If check_out date has passed, we need to check the slots for that day
                    if ($check_out->format('Y-m-d') <= $today) {
                        $slots = $booking->hourly_slots->where("date", $check_out->format("Y-m-d"));

                        if ($slots->count() > 0) {
                            $allSlotsPassed = true;

                            foreach ($slots as $slot) {
                                // Parse the slot time (format is typically "21:00 - 22:00")
                                $slotParts = explode(' - ', $slot->slot);

                                if (count($slotParts) == 2) {
                                    $slotEndTime = Carbon::parse($check_out->format('Y-m-d') . ' ' . $slotParts[1]);

                                    // If any slot's end time is in the future, not all slots have passed
                                    if ($slotEndTime > $currentTime) {
                                        $allSlotsPassed = false;
                                        break;
                                    }
                                }
                            }

                            // If all slots have passed, mark the booking as completed
                            if ($allSlotsPassed) {
                                $booking->status = 3; // Completed
                                $booking->save();
                                $completedBookings++;
                            }
                        } else {
                            // If no slots found for the check_out date but the date has passed,
                            // mark as completed (fallback)
                            $booking->status = 3; // Completed
                            $booking->save();
                            $completedBookings++;
                        }
                    }
                }
            }
        }

        return [
            "message" => "Cron successfully done",
            "completed_bookings" => $completedBookings,
            "today" => today()->format("Y-m-d H:i:s")
        ];
    }
    public function walletAmount()
    {
        // Fetch all the bookings with status 3 (completed) and is_wallet = 0 (wallet not updated)
        $bookings = Booking::where("status", 3)
            ->where('is_wallet', 0)
            ->get();
        foreach ($bookings as $booking) {
            // Fetch the provider's completed bookings sorted by the earliest first
            $providerBookings = Booking::where('provider_id', $booking->provider_id)
                //->where('status', 3) // Only consider completed bookings
                ->orderBy('check_out', 'asc')  // Sort by earliest check_out date
                ->get();
            // Check if this is the first booking for this provider
            $firstBooking = $providerBookings->first(); // Get the first completed booking
            if ($firstBooking) {
                // Check if it's the first booking (i.e., there's no previous booking in the last 30 days)
                // $firstBookingCheck = $providerBookings->where('check_out', '>=', Carbon::now()->subDays(30))->count();
                $firstBookingCheck = $providerBookings->where('check_out', '>=', Carbon::parse($firstBooking->check_out)->subDays(30))->count();
                // If it's the first booking and it's after 30 days of checkout, add the amount to wallet
                if ($firstBookingCheck == 1 && Carbon::parse($firstBooking->check_out)->addDays(30)->isPast()) {
                    // Get the tax rate safely, defaulting to a value if listing or category is null
                    $taxRate = 0; // Default tax rate
                    if ($booking->listing && $booking->listing->category) {
                        $taxRate = $booking->listing->category->tax;
                    }
                    
                    $serviceProviderAmount = $booking->sub_total - get_percentage($taxRate, $booking->sub_total);
                    $wallet = Wallet::where('user_id', $booking->provider_id)->first();
                    
                    if ($wallet) {
                        $wallet->amount += $serviceProviderAmount;
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $wallet->commission_amount += get_percentage($taxRate, $booking->sub_total);
                        }
                        $wallet->save();
                    } else {
                        $walletData = [
                            'user_id' => $booking->provider_id,
                            'amount' => $serviceProviderAmount,
                        ];
                        
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $walletData['commission_amount'] = get_percentage($taxRate, $booking->sub_total);
                        }
                        
                        Wallet::create($walletData);
                    }
                    
                    $booking->is_wallet = 1;
                    $booking->save();
                }
                // For subsequent bookings, add the amount after 48 hours of the checkout
                elseif ($firstBookingCheck > 1 && Carbon::parse($booking->check_out)->addHours(48)->isPast()) {
                    // Get the tax rate safely, defaulting to a value if listing or category is null
                    $taxRate = 10; // Default tax rate
                    if ($booking->listing && $booking->listing->category) {
                        $taxRate = $booking->listing->category->tax;
                    }
                    
                    $serviceProviderAmount = $booking->sub_total - get_percentage($taxRate, $booking->sub_total);
                    $wallet = Wallet::where('user_id', $booking->provider_id)->first();
                    
                    if ($wallet) {
                        $wallet->amount += $serviceProviderAmount;
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $wallet->commission_amount += get_percentage($taxRate, $booking->sub_total);
                        }
                        $wallet->save();
                    } else {
                        $walletData = [
                            'user_id' => $booking->provider_id,
                            'amount' => $serviceProviderAmount,
                        ];
                        
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $walletData['commission_amount'] = get_percentage($taxRate, $booking->sub_total);
                        }
                        
                        Wallet::create($walletData);
                    }
                    
                    $booking->is_wallet = 1;
                    $booking->save();
                }
            }
        }
        return ["message" => "Wallet Cron successfully executed", "today" => today()->format("Y-m-d H:i:s")];
    }
    function cancellationAmount()
    {
        $bookings = Booking::where("status", 7)->where("is_wallet", 0)
            ->whereDate('check_out', '<=', Carbon::now()->subHours(48)->toDateString())
            ->get();
        foreach ($bookings as $booking) {
            $wallet = Wallet::where('user_id', $booking->provider_id)->first();
            
            // Get tax rate safely
            $taxRate = 10;
            if ($booking->listing && $booking->listing->category) {
                $taxRate = $booking->listing->category->tax;
            }
            
            $refund_amount = $booking->service_refund_amount_cop - get_percentage($taxRate, $booking->service_refund_amount_cop);
            
            if ($refund_amount > 0) {
                if ($wallet) {
                    $wallet->amount += $refund_amount;
                    $wallet->save();
                } else {
                    Wallet::create([
                        'user_id' => $booking->provider_id,
                        'amount' => $refund_amount,
                    ]);
                }
                $booking->is_wallet = 1;
                $booking->save();
            }
        }
        return api_response(true, "Wallet Cancel Cron job successfully executed!");
    }
    public function scheduledAmount()
    {
        $users = User::whereNotNull('schedule_amount')->get();
        foreach ($users as $user) {
            $wallet = Wallet::where('user_id', $user->id)->first();
            if ($wallet && $wallet->amount >= $user->schedule_amount && $user->schedule_active == 1) {
                $usd_conversion_rate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate');
                $usd_conversion_rate = $usd_conversion_rate ? $usd_conversion_rate : 1;
                $amountInUsd = $user->schedule_amount * $usd_conversion_rate;
                $this->refreshAccessToken();
                $accessToken = session('tipalti_access_token');
                if (!$accessToken) {
                    return back()->with(["message" => "Missing Tipalti Access Token", "type" => "error"]);
                }
                if ($amountInUsd >= 50) {
                    $reference_code = uniqid();
                    $response = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ])->post('https://api-sb.tipalti.com/api/v1/payment-batches', [
                        'paymentInstructions' => [
                            [
                                'amountSubmitted' => [
                                    'amount' => $user->schedule_amount,
                                    'currency' => 'COP',
                                ],
                                'refCode' => (string)$reference_code,
                                'payeeId' => $user->payee_id,
                            ]
                        ]
                    ]);
                    $data = $response->json();
                    if ($response->successful()) {
                        $withdrawal_request = new WithdrawalRequest();
                        $withdrawal_request->user_id = $user->id;
                        $withdrawal_request->reference_code = $reference_code;
                        $withdrawal_request->currency = 'COP';
                        $withdrawal_request->amount = $user->schedule_amount;
                        $withdrawal_request->wallet_amount = $wallet->amount;
                        $withdrawal_request->status = 0; // Pending status
                        $withdrawal_request->save();
                        // Deduct the requested amount from the wallet
                        $wallet->amount -= $user->schedule_amount;
                        $wallet->save();
                        // Return success response
                        Log::info('Withdrawal request for user ' . $user->id . ' processed successfully.');
                    } else {
                        Log::error('Error in sending withdrawal request to Tipalti: ' . json_encode($data));
                    }
                } else {
                    Log::warning('Amount less than required USD amount for user ' . $user->id);
                }
                // } else {
                //     Log::warning('Wallet amount less than wallet threshold for user ' . $user->id);
                // }
            }
        }
        return "Scheduled Wallet Cron job successfully executed!";
    }
    public function refreshAccessToken()
    {
        $client = new Client();
        $refreshToken = CommonSetting::first()->refresh_token ?? '';
        // $refreshToken = session()->get('tipalti_refresh_token');
        if (!$refreshToken) {
            //return response()->json(["error" => "Refresh token is missing."], 400);
            return redirect()->route('tipalti.auth');
        }
        try {
            $response = $client->post('https://sso.sandbox.tipalti.com/connect/token', [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);
            $tokenData = json_decode($response->getBody(), true);
            session()->put('tipalti_access_token', $tokenData['access_token']);
            session()->put('tipalti_refresh_token', $tokenData['refresh_token']);
            session()->put('tipalti_token_expires_in', now()->addSeconds($tokenData['expires_in']));
            return response()->json(["message" => "Access token refreshed successfully", "token" => $tokenData]);
        } catch (\Exception $e) {
            return response()->json(["error" => "Failed to refresh access token", "message" => $e->getMessage()], 400);
        }
    }
    function delete_draft_listing()
    {
        $draft_listings = Listing::where("status", 6)
            ->whereDate('created_at', '<=', Carbon::now()->subDays(30))
            ->get();
        foreach ($draft_listings as $draft_listing) {
            $this->listingService->deleteListing($draft_listing->id);
        }
        return api_response(true, "Draft Listing Deleted");
    }
}
