<?php

namespace App\Listeners;

use App\Models\UserSession;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class LogUserLogin
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        UserSession::create([
            'user_id' => $event->user->id,
            'ip_address' => request()->ip(),
            'login_time' => now(),
            'session_id' => session()->getId(),
        ]);

    }
}
