{"about": "About", "about_site": "About Site", "about_us": "About Us", "accessibility": "Accessibility", "account_holder_name": "Account Holder Name", "action": "Action", "actions": "Actions", "active": "Active", "active_booking": "Active Bookings", "active_bookings": "Active Bookings", "activity_log": "Activity Log", "add": "Add", "add_admin": "Add Admin", "add_amenities": "Add Amenities", "add_boat": "Add Boat", "add_car": "Add Car", "add_custom_amenities": "Add Custom Amenities", "add_discounts": "Add discounts", "edit_temp": "Edit Template", "add_listing": "Add Listing", "add_listings": "Add Listings", "add_medical": "Add Medical", "add_medical_tourism": "Add Medical Tourism", "add_new_user": "Add New User", "add_residence": "Add Residence", "add_tour": "Add Tour", "address": "Address", "admin_name": "Admin Name", "admins": "Admins", "adult_price": "Adult price", "advance_booking": "How far in advance can guests book?", "advance_filter": "Filters", "advance_listing": "Advance your listing", "advance_notice": "Advance Notice", "advance_notice_time": "Advance Notice Time", "all": "All", "all_bookings": "All Bookings", "all_listings": "All Listings", "my_listings": "My Listings", "all_messages": "All Messages", "allow_pets": "Do you allow Pets?", "allowed": "allowed", "allows_you_to_increase_price": "Allows you to increase the price of your listing for a specific period of time", "amenities": "Amenities", "amenities_1": "Amenities 1", "amenity": "Add Amenity", "amenity_id": "Amenity Id", "amenity_name": "Amenity Name", "amount": "Amount", "amount_paid": "Amount <PERSON>", "answer": "Answer", "apartment": "Apartment #8", "apply": "apply", "approve": "Approve", "approved": "Approved", "apt_suite_unit_optional": "Apt, Suite, Unit (Optional)", "automatic": "Automatic", "back": "Back", "banner_image": "Banner Image", "bathrooms": "Bathrooms", "bedrooms": "Bedrooms", "beds": "Beds", "block": "Block", "booking_capacity": "Booking Capacity", "booking_name": "Booking Name", "browse_website": "Browse Website", "calendar": "Calendar", "cancellation_policy": "Cancellation policy", "capacity": "Capacity", "capacity_optional": "Capacity (Optional)", "card_detail": "Card Detail", "categories": "Categories", "categories_english": "Categories (English)", "categories_spanish": "Categories (Spanish)", "checkin_checkout": "Choose Your Booking Dates", "children": "Children", "choose_cancellation_policy": "Choose your cancellation policy", "language": "Language", "choose_language": "Choose Language", "choose_location": "Choose location", "city_town": "City/Town", "cms": "CMS", "company_name": "Company Name", "conditions_met": "Conditions Met", "confirm_few_details_and_publish": "Confirm few details and publish", "confirm_your_address": "Confirm Your Address", "connecting": "Connecting", "contact": "Contact", "contact_detail": "Contact Detail", "contact_host": "Contact Host", "contact_us": "Contact us", "content_management_system": "Content Management System", "country_region": "Country/Region", "create_listing": "Create Listing", "create_new": "Create New", "crud_generator": "CRUD Generator", "current_balance": "Current Balance", "current_wallet_amount": "Current Wallet Amount", "pending_wallet_amount": "Pending Wallet Amount", "customers": "Customers", "cvc": "CVC", "cvt": "CVT", "daily": "Daily", "dashboard": "Dashboard", "date": "Date", "date_from": "From", "until": "Until", "date_to": "Date To", "dated_from": "Dated From", "dated_to": "Dated To", "day": "Day", "day_iteration_start_end_time": "day :iteration start & end time", "define_child_age": "Define Child Age", "delete": "Delete", "deleted_users": "Deleted Users", "describe_space": "Describe your :name", "description": "Description", "desigination": "Designation", "do_you_want_to_block_these_days": "Do You Want To Block These Days?", "draft": "Draft", "drafts": "Drafts", "your": "Your ", "e_wallet": "E-Wallet", "earning_in_a_period": "Earning In A Period", "edit_category": "Edit Category", "edit": "Edit", "electric": "Electric", "email": "Email", "engine_type": "Engine Type", "enhance_your_space": "Enhance your :name by highlighting its unique amenities and presenting it with a collection of five or more appealing photos. Then, craft a title and description that captivate.", "equipments_of_sailboat": "Equipments of the sailboat", "experience": "Experience", "extra": "Extra", "facebook": "Facebook", "favicon": "Favicon", "favorites": "Favorites", "filter": "Filter", "finally_choose_booking_settings_set_pricing_publish_listing": "Finally, you’ll choose booking settings, set up pricing, and publish your listing", "first_name": "First Name", "follow_us": "Follow Us", "footer": "Footer", "for_stays_of_30_nights_or_more": "For stays of 30 nights or more", "for_stays_of_7_nights_or_more": "For stays of 7 nights or more", "for_up_to_3_bookings": "For up to 3 bookings", "from": "From", "full_refund": "For a full refund, cancel at least 24 hours in advance of the start date of the experience.", "gasoline": "Gasoline", "get_started": "Get started", "group_discount": "Group Discount", "help_center": "Help Center", "help_your_listing_stand_out": "Help your :category_name stand out to get booked faster and earn your first reviews.", "home": "Home", "host_reviews": "Host Reviews", "hourly": "Hourly", "hybrid": "Hybrid", "id": "ID", "image": "Image", "inactive": "Inactive", "inbox": "Inbox", "inbox_messages": "Inbox Messages", "included_activities": "Included Activities", "included_equipment": "Included Equipment", "instagram": "Instagram", "it_can_accommodate": "It can accommodate.", "itinerary": "Itinerary", "laravel_log": "<PERSON><PERSON>", "last_active": "Last Active", "last_digits": "Last Digits", "last_name": "Last Name", "length": "Length", "let_s_review_your_listing": "Let's review your :category_name", "lets_finish_up_publish_your": "Let's finish up & publish your :name", "life_earning": "Lifetime Earning", "lifetime_commisions": "Lifetime Commisions", "listing_availability": "Listing Availability", "listing_id": "Listing Id", "listing_name": "Listing Name", "category": "Category", "listings": "Listings", "location": "Location", "login": "<PERSON><PERSON>", "logo": "Logo", "logout": "Logout", "make_your_category_stand_out": "Make your :name stand out!", "make_your_name_stand_out": "Make your :name stand out!", "manage_users": "Manage Users", "manual": "Manual", "message": "Message", "messages": "Messages", "messges": "MESSAGES", "min_nights_rent": "What's the minimum nights you will rent your place for?", "min_stay_length": "Minimum Stay Length", "month": "Month", "monthly_discount": "Monthly Discount", "multiple_days": "multiple days", "my_bookings": "My Bookings", "name": "Name", "navigation_bar": "Navigation Bar", "new": "New", "new_listing_discount": "New listing discount", "next_paragraph_tip": "To move to the next paragraph, press (shift + enter)", "no": "No", "no_booking_found": "No Booking Found", "no_contact_message": "No Contact Message", "no_data_found": "No data found", "no_detail_found": "No Detail Found", "no_internet_access": "No internet access", "no_listings": "You Don’t Have Any Listings", "no_report_found": "No Report Found", "no_reviews": "No Reviews", "no_withdrawal_request": "No Withdrawal Request Yet", "no_withdrawal_request_yet": "No withdrawal requests yet", "no_payments": "No Payments Yet", "not_allowed": "not allowed", "notes": "Notes", "notice_requirement": "How much notice do you need between a guest booking and their arrival?", "notifications": "Notifications", "now_set_your_price_for_child": "Now, set your price for child", "number_of_people": "No of people", "on_going_bookings": "On Going Bookings", "one_month_advance": "1 month in advance", "one_week_advance": "1 Week in advance", "optional": "Optional", "incoming_payments": "Incoming Payments", "available_withdrawl": "Available Withdrawal", "in_transit": "In Transit", "total_cancellation": "Total Cancellation", "adjusments": "Adjusments", "overall_bookings": "Overall Bookings", "overall_rating": "Overall Rating", "overview": "Overview", "earning_insights": "Earning Insights", "paragraph_instruction": "To move to next paragraph, press (shift + enter)", "passengers": "Passengers", "past_bookings": "Past Bookings", "pay_now": "Pay Now", "payments": "Payments", "manage_payouts": "Payout Management", "configure_payments": "Payout Method", "pending": "Pending", "pending_approval": "Pending Approval", "pending_reports": "Pendings Reports", "phone": "Phone", "phone_number": "Phone Number", "please_start_a_chat_to_messaging": "Please select a chat to start messaging", "price_change": "Price change", "price_range": "Price Range", "privacy_policy": "Privacy Policy", "profile_name": "Profile Name", "provider_name": "Provider Name", "question": "Question", "question_1": "Question 1", "quick_links": "Quick Links", "rating": "Rating", "ratings": "Rating", "read_more": "Read More", "reject": "Reject", "rent_a_house_in_norway": "Rent a house in Norway", "reply": "Reply", "reported_by": "Reported By", "reports": "Reports", "reset": "Reset", "resolve": "Resolve", "resolved": "Resolved", "review": "Review", "review_detail": "Review Detail", "reviews": "reviews", "rnt_license": "RNT License", "role": "Role", "rules": "Rules", "s_no": "S No.", "same_day": "same day", "search": "Search", "season_end": "Season end", "season_start": "Season start", "seasonal_pricing": "Seasonal Pricing", "seats": "Seats", "section": "Section", "see_departure_details": "See departure details", "select": "Select", "select_amenity": "Select Amenity", "select_date": "Choose date", "select_role": "Select Role", "select_status": "Select Status", "selection_field": "Selection Field", "send": "Send", "sender_bank_account_details": "Sender Bank Account Details", "service_name": "Service Name", "service_providers": "Service Providers", "settings": "Settings", "share_amenities": "Share with guests the amenities of your place.", "share_details_category": "Share with guests the details of your :name", "share_some_basics_about_your_name": "Share some basics about your :name", "share_time_of_your_experience": "Share time of your experience", "sign_up": "Sign Up 1", "social_links": "Social Links", "space_unique": "Share what makes your space unique", "standout_amenities_question": "Do you have any standout amenities?", "star_amenities_key_feature": "Star amenities that are your key feature", "start_end_time": "start & end time", "state_territory": "State/Territory", "status": "Status", "step_1_title": "Tell us about your :name", "step_2": "Step 2", "step_3": "Step 3", "street_address": "Street/Address", "sub_admin": "Sub Admin", "subject": "Subject", "submit": "Submit", "super_admin": "Super Admin", "suspend": "Suspend", "suspended": "Suspended", "tell_guests_about_rules": "Tell guests about your :name rules", "term_conditions": "Terms & Conditions", "this_is_what_your_guests_will_see": "This is what your guests will see, make sure everything is correct.", "guests": "Guests", "today": "Today", "this_week": "This Week", "last_month": "Last Month", "last_quarter": "Last Quarter", "last_year": "Last Year", "this_month": "This Month", "this_year": "This Year", "tiktok": "Tiktok", "time": "Time", "title": "Title", "title_prompt": "Now, let's come up with a title for your :name.", "title_suggestion": "Opt for brief titles. Enjoy the creativity; changes can be made at any time.", "to": "To", "total_booking": "Total Bookings", "total_bookings": "Total Bookings", "total_platform_fee": "<PERSON><PERSON>", "total_expenditure": "Total Expenditure", "total_listing": "Total Listings", "total_listings": "Total Listings", "total_earning": "Total Earning", "tour_will_start_on": "Tour will start on", "transmission": "Transmission", "two_months_advance": "2 months in advance", "type": "Type", "type_of_name": "Type Of :name", "type_search_field": "Type Search Field", "type_to_search": "Type to search..", "unresolved": "Unresolved", "unsuspend": "Unsuspend", "upcoming_bookings": "Upcoming Bookings", "update": "Update", "user_details": "User Details", "user_id": "User Id", "user_management": "User Management", "users_chat": "User's Chat", "view": "View", "view_all": "View All", "view_all_reviews": "View All Reviews", "view_invoice": "View Invoice", "view_webpage": "View Listing", "we_ll_inquire_about_the_type_of_name_you_own_afterward_please_provide_details_regarding_its_location_and_the_maximum_number_of": "We'll inquire about the type of :name you own. Afterward, please provide details regarding its location and the maximum number of", "we_ll_let_you_know_if_you_need_to_verify_identity": "We’ll let you know if you need to verify your identity or register with the local government.", "weekly_discount": "Weekly Discount", "what_are_your_rules": "What are your rules?", "what_s_next": "What’s Next?", "what_you_looking_for": "What are you looking for", "whats_included": "What's included", "whats_not_included": "What's not included", "where_is_the_start_point": "Where's the start point?", "where_is_your_name_located": "Where’s your :name located?", "withdrawal_request": "Withdrawal Request", "withdrawal_requests": "Withdrawal Requests", "year": "Year", "yes": "Yes", "you_get_picked_up": "You’ll get picked up", "you_will_return_to_the_starting_point": "You'll return to the starting point", "your_address_is_shared_with_guests_only_once_they_have_completed_a_reservation": "Your address is shared with guests only once they have completed a reservation.", "your_browser_not_support_the_html_video": "Your browser does not support HTML video.", "your_space": "Your Space", "youtube": "Youtube", "drop_photos_here": "Drop Photos Here", "choose_at_least_5_images": "Choose at least 5 Images", "zip_code": "Zip Code", "Which of these describe your service?": "Which of these describe your service?", "revenue_generated": "Revenue Generated", "Save & Exit": "Save & Exit", "not_included": "Not Included", "add_photos": "Add Photos", "add_high_quality_photos": "Add High Quality Photos", "you_can_change_it_anytime": "You can change it anytime", "guest_price_after_commissions_will_be": "Guest price after commissions will be", "learn_more_about_pricing": "Learn more about pricing", "now_set_your_price_for_adult": "Now set your price for adult", "Step": "Step", "now_set_your_price": "Now set your price", "you_dont_have_any_listings": "You don't have any listings", "would_you_prefer_to_rent_on_an_hourly_or_daily_basis": "Would you prefer to rent on an hourly or daily basis", "key_features": "Key Features", "add_documents_as_evidence": "Add documents as evidence", "provide_proof_of_ownership": "Provide proof of ownership", "document_pdf_format": "Document should be in PDF format", "drop_document_here": "Drop document here", "all_rights_reserved": "All Rights Reserved", "terms_conditions": "Terms & Conditions", "customer_name": "Customer Name", "report_detail": "Report Detail", "amenity_detail": "Amenity Detail", "help_detail": "Help Center Detail", "in_eng": "in English", "in_spanish": "in Spanish", "enter": "Enter", "recent_activity": "Recent Activity", "email_temp": "Email Template Detail", "user_name": "User Name", "drop_documents_here": "Drop documents here", "prepare_your_ownership_documents": "Prepare Your Ownership Documents", "booking_id": "Booking ID", "supplier_agreement": "Supplier's Agreement", "review_by": "Review By", "edit_user": "Edit User"}