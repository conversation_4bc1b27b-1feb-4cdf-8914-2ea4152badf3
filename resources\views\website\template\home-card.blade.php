@foreach ($listings as $listing)
    @isset($listing)
        <div class="{{ $search_data == true ? 'col-lg-4' : 'col-lg-3' }} col-md-6">
            <div class="product_box">
                <a href="{{ url('detail') . '/' . $listing->ids . '/' . $listing->slug }}" target="_blank">
                    <div class="product_img">
                        <div class="product_wish d-flex align-items-center justify-content-between px-3">
                            <div class="client">
                                <img src="{{ asset('website') . '/' . $listing->user->avatar }}">
                            </div>
                            @auth
                                <input type="checkbox" class="heart d-none" value="{{ $listing->ids }}"
                                    id="wishlist{{ $listing->ids }}" @if ($listing->wishlist) checked @endif>

                                <label for="wishlist{{ $listing->ids }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <path
                                            d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.69001C2 5.60001 4.49 3.10001 7.56 3.10001C9.38 3.10001 10.99 3.98001 12 5.34001C12.5138 4.64588 13.183 4.08173 13.954 3.69275C14.725 3.30377 15.5764 3.10077 16.44 3.10001C19.51 3.10001 22 5.60001 22 8.69001C22 15.69 15.52 19.82 12.62 20.81Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </label>
                            @else
                                <input type="checkbox" class="d-none" value="{{ $listing->ids }}"
                                    id="wishlist{{ $listing->ids }}">
                                <label for="wishlist{{ $listing->ids }}" data-bs-toggle="modal" data-bs-target="#login">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <path
                                            d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.69001C2 5.60001 4.49 3.10001 7.56 3.10001C9.38 3.10001 10.99 3.98001 12 5.34001C12.5138 4.64588 13.183 4.08173 13.954 3.69275C14.725 3.30377 15.5764 3.10077 16.44 3.10001C19.51 3.10001 22 5.60001 22 8.69001C22 15.69 15.52 19.82 12.62 20.81Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </label>
                            @endauth
                        </div>
                     
                        <div class="swiper productSwiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <div class="slide-img">
                                        <img src="{{ asset('website') . '/' . ($listing->thumbnail_image->url ?? '') }}"
                                            alt="{{ $listing->thumbnail_image->name ?? '' }}" loading="lazy"
                                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                    </div>
                                </div>

                                @foreach ($listing->gallery_images as $key => $image)
                                    @if ($image->type == 'image' && $key != 0)
                                        <div class="swiper-slide">
                                            <div class="slide-img">
                                                <img src="{{ asset('website') . '/' . $image->url }}"
                                                    alt="{{ $image->type }}" loading="lazy"
                                                    onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                            </div>
                                        </div>
                                    @endif

                                {{-- @empty
                                    <div class="swiper-slide">
                                        <div class="slide-img">
                                            <img src="{{ asset('website/images/plcaeholderListingImg.png') }}">
                                        </div>
                                    </div> --}}
                                @endforeach
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                    <div class="rating">
                        <h5 class="product_heading ">{{ $listing->name }}</h5>
                        {{-- @if($listing->rating > '0') --}}
                        <p class="star-rate m-0"><i class="fas fa-star"></i>
                            {{ $listing->rating == 0 ? 'New' : $listing->rating }}</p>
                        {{-- @endif --}}
                        <p  class="star-rate"><strong>Score:</strong> {{ $listing->score ?? 'N/A' }}</p>
                        @if (isset($listing->details['proximity']) &&
                                isset($listing->details['click_rate']) &&
                                isset($listing->details['bookings']) &&
                                isset($listing->details['pricing']) &&
                                isset($listing->details['rating']) &&
                                isset($listing->details['favorites']))
                            <ul>
                                <li><strong>Proximity:</strong> {{ $listing->details['proximity'] }}%</li>
                                <li><strong>Click Rate:</strong> {{ $listing->details['click_rate'] }}%</li>
                                <li><strong>Bookings:</strong> {{ $listing->details['bookings'] }}%</li>
                                <li><strong>Pricing:</strong> {{ $listing->details['pricing'] }}%</li>
                                <li><strong>Rating:</strong> {{ $listing->details['rating'] }}%</li>
                                <li><strong>Favorites:</strong> {{ $listing->details['favorites'] }}%</li>
                            </ul>
                        @endif

                    </div>
                    <div class="listing_desc">{!! str_limit($listing->description, 35, '...') !!}</div>
                    <span class="span-italic">
                        @php
                            $new_listing_discount = newListingDiscount($listing->price, count($listing->active_bookings ?? 0), $listing->discount);
                        @endphp
                        @if ($new_listing_discount["total_amount"] == $listing->price)
                            {{ session('currency', 'COP') . ' ' . number_format($listing->price * session('conversion_rate', 1), 0) . ' / ' . $listing->detail->basis_type }}                        
                        @else
                            <del>{{ session('currency', 'COP') . ' ' . number_format($listing->price * session('conversion_rate', 1), 0) }}</del>
                            {{ session('currency', 'COP') . ' ' . number_format($new_listing_discount["total_amount"] * session('conversion_rate', 1), 0) . ' / ' . $listing->detail->basis_type }}                        
                        @endif
                        {{-- @if (isset($listing->detail->basis_type) || $listing->category_id == 1)
                            @if ($listing->detail->basis_type == 'Hourly')
                                {{ session()->get('currency') ?? 'COP' }}
                                {{ number_format($listing->detail->per_hour * (session('conversion_rate') ?? 1), 2) }} /
                                hourly
                            @elseif ($listing->detail->basis_type == 'Daily')
                                {{ session()->get('currency') ?? 'COP' }}
                                {{ number_format($listing->detail->per_day * (session('conversion_rate') ?? 1), 2) }} / day
                            @elseif ($listing->category_id == 1)
                                {{ session()->get('currency') ?? 'COP' }}
                                {{ number_format($listing->detail->adult_price * (session('conversion_rate') ?? 1), 2) }} /
                                per adult
                            @endif
                        @endif --}}
                    </span>
                </a>
            </div>
        </div>
    @endisset
@endforeach
