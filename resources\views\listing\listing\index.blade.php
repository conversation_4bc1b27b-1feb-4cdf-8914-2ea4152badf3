@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    {{-- <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" /> --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
@endpush

@section('content')
    <section class="head_btn">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    @if (auth()->user()->hasRole('service'))
                        <h1>{{ trans('my_listings') }}</h1>
                    @else
                        <h1>{{ trans('all_listings') }}</h1>
                    @endif
                </div>
            </div>
        </div>
    </section>
    <div class="table_sect lisiting_table">
        <div class="container-fluid">
            <div class="custom_btns rounded_btn pt-4 flex-wrap">
                <div class="btns d-flex gap-1">
                    <div class="head_bt">
                        {{-- {{ url('category') }} --}}
                        @if (auth()->user()->listing_consent != 1)
                            <a href="javascript:void(0)" class="btn btn_yellow" data-toggle="modal"
                                data-target="#add_listing_disclaimer_modal"> {{ trans('add_listing') }}</a>
                        @else
                            <a href="{{ url('category') }}" class="btn btn_yellow"> {{ trans('add_listing') }}</a>
                        @endif
                        {{-- @if (auth()->user()->hasRole('user'))
                            <a href="{{ url('add-medical') }}" class="btn btn_yellow">{{ trans('add_medical') }}</a>
                        @endif --}}
                        {{-- <button class="btn btn_yellow" data-toggle="modal" data-target="#listModal"
                            data-whatever="@mdo"> Add Listings</button> --}}
                    </div>
                    <!-- Bulk Delete Button (Initially Hidden) -->
                    <button id="delete-button" class="btn btn-danger pull-left btn_yellow btn_delete" style="display: none;"
                        data-toggle="modal" data-target="#confirmationModal">Delete
                        Selected
                    </button>
                </div>
                <div class="custom_btns search_btns">
                    <div class="nav_search main">
                        <!-- Actual search box -->
                        <div class="form-group has-feedback has-search m-0">
                            <form class="example" action="/action_page.php" style="width: 100%">
                                <button type="button"><i class="fa fa-search"></i></button>
                                <input type="text" placeholder="Search.." id="searchBar" name="search2">
                                {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                            </form>
                        </div>
                    </div>
                    @if (auth()->user()->hasRole('service'))
                        <div class="head_bt">
                            <button class="active view_change"
                                data-view="@if (auth()->user()->hasRole('service')) list @else grid @endif ">
                                <i class="fas fa-list-ul @if (!auth()->user()->hasRole('service')) d-none @endif"></i>
                                <i class="fas fa-th-large @if (auth()->user()->hasRole('service')) d-none @endif"></i>
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            <div class="listing_parent_wrapper pt-2">
                <div class="row">
                    <div class="col-sm-12">
                        <h3 class="text-danger semi-bold mt-0 draft_text">All drafts will be permanently deleted after 30
                            days.</h3>
                        <div class="white-box  @if (auth()->user()->hasRole('service')) no_shadow @endif">
                            <div class="listing_filters_wrapper @if (auth()->user()->hasRole('service')) card-filter @endif">
                                <div class="custom_radio_wapper">
                                    <label for="all_listings" class="active">All</label>
                                    <input type="radio" name="listing_filter" id="all_listings" value="0" checked>
                                </div>
                                @if (!auth()->user()->hasRole('service'))
                                    <div class="custom_radio_wapper">
                                        <label for="published_listings">
                                            Published
                                        </label>
                                        <input type="radio" name="listing_filter" id="published_listings" value="1">
                                    </div>
                                    <div class="custom_radio_wapper">
                                        <label for="pending_listings">
                                            Pending
                                        </label>
                                        <input type="radio" name="listing_filter" id="pending_listings" value="2">
                                    </div>
                                @else
                                    <div class="custom_radio_wapper">
                                        <label for="accepted_listings">
                                            Accepted
                                        </label>
                                        <input type="radio" name="listing_filter" id="accepted_listings" value="3">
                                    </div>
                                    <div class="custom_radio_wapper">
                                        <label for="in_review_listings">
                                            In Review
                                        </label>
                                        <input type="radio" name="listing_filter" id="in_review_listings" value="4">
                                    </div>
                                @endif
                                <div class="custom_radio_wapper">
                                    <label for="drafts">
                                        Drafts
                                    </label>
                                    <input type="radio" name="listing_filter" id="drafts" value="5">
                                </div>

                            </div>
                            <div class="clearfix"></div>

                            <div class="table-responsive list_view @if (auth()->user()->hasRole('service')) d-none @endif">
                                <table class="table listing_table" id="myTable">
                                    <thead>
                                        <tr>
                                            <th class="no_draft"><input type="checkbox" id="check-all" /></th>

                                            @if (!auth()->user()->hasRole('service'))
                                                <th>#</th>
                                                <th>{{ trans('Provider Image') }}</th>
                                                <th scope="col" class="text-center">{{ trans('listing_name') }}</th>
                                            @else
                                                <th colspan="2" class="text-center">{{ trans('Listing') }}</th>
                                                <th class="text-center">{{ trans('Internal Name') }}</th>
                                            @endif

                                            <th scope="col">{{ trans('type') }}</th>
                                            <th scope="col" class="text-center no_draft">
                                                {{ trans('active_bookings') }}
                                            </th>
                                            <th scope="col" class="no_draft">{{ trans('revenue_generated') }}</th>
                                            <th scope="col">{{ trans('status') }}</th>
                                            <th scope="col" class="no_draft">{{ trans('ratings') }}</th>
                                            <th scope="col" class="for_draft">Days Remaining</th>
                                            <th scope="col" class="no_draft">{{ trans('calendar') }}</th>
                                            <th scope="col">{{ trans('action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($listing as $list)
                                            {{-- @if ($item->status != 6) --}}
                                            @include('listing.listing.layout.list_view')
                                            {{-- @else --}}



                                            {{-- @endif --}}
                                        @empty
                                            <tr>
                                                <td colspan="11" style="text-align: center">
                                                    {{ trans('you_dont_have_any_listings') }}</td>
                                            </tr>
                                        @endforelse

                                    </tbody>
                                </table>
                                <div class="pagination-wrapper"> {!! $listing->appends(['search' => Request::get('search')])->render() !!} </div>
                            </div>
                            @if (auth()->user()->hasRole('service'))
                                <div
                                    class="row listing_card_wrapper card_view @if (auth()->user()->hasRole('service')) d-block @endif">
                                    @forelse ($listing as $list)
                                        @include('listing.listing.layout.card_view')
                                        {{-- @else --}}
                                        {{-- <div class="text-center poppins-medium text-dark-black test">
                                                {{ trans('you_dont_have_any_listings') }}</div>
                                            @break --}}
                                        {{-- @endif --}}
                                    @empty
                                        <div class="text-center poppins-medium text-dark-black testing ">
                                            {{ trans('you_dont_have_any_listings') }}</div>
                                    @endforelse
                                    <div class="text-center poppins-medium text-dark-black testing not_found">
                                        {{ trans('you_dont_have_any_listings') }}</div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- New Modal --}}
    <section class=" reject">
        <div class="modal fade" id="reject" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog" id="amenModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="">{{ trans('reject') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <form action="{{ route('reject_listing') }}" method="POST">
                                    @csrf
                                    <input type="hidden" name="listing_id" id="listing_id">
                                    <div class="form_field_padding">
                                        <div class="cust_select mb-3">
                                            <textarea class="form-control" name="reason" id="" cols="30" rows="10"
                                                placeholder="Type Message.."></textarea>
                                        </div>
                                    </div>
                                    <div class=" modal_btn">
                                        <button type="button " class="btn yellow"
                                            id="amenity_btn_title">{{ trans('send') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- View document modal --}}
    @include('modals.view-doc')
    {{-- View document modal end --}}

    {{-- View internal_name modal --}}
    @include('modals.internal_name')
    {{-- View internal_name modal end --}}


    {{-- suspend modal --}}
    @include('modals.listing-suspend')
    {{-- suspend modal end --}}

    {{-- listing delete modal --}}
    @include('modals.listing-delete')
    {{-- listing delete modal end --}}

    {{-- confimation modal --}}
    @include('modals.confirmation')
    {{-- confimation moda; end --}}

    {{-- disclaimer modal --}}
    @include('modals.disclaimer')
    {{-- disclaimer modal end --}}




@endsection

@push('js')
    {{-- <script src="{{ asset('js/db1.js') }}"></script> --}}
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->

    <script type="text/javascript">
        $(document).ready(function() {
            $(document).on('click', '.pdf-preview, .image_wrapper', function(e) {
                if ($(e.target).closest('.download').length > 0) {
                    return;
                }
                var link = $(this).closest('.upload_doc_view').attr('data-document-url');
                console.log(link);
                window.open(
                    link,
                    'PDFPopup',
                    'width=800,height=600,resizable=yes,scrollbars=yes'
                );
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.view_change').on('click', function() {
                var viewBtn = $(this).attr('data-view').toLowerCase().trim();
                var $cardView = $('.card_view');
                var $listView = $('.list_view');

                $(this).find('.fa-list-ul').toggleClass('d-none');
                $(this).find('.fa-th-large').toggleClass('d-none');


                // console.log(viewBtn);
                if (viewBtn == 'grid') {
                    $(this).attr('data-view', 'list');
                    $listView.fadeOut(150, function() {
                        $cardView.fadeIn(150);
                        $('.white-box').addClass('no_shadow');
                        $('.listing_filters_wrapper').addClass('card-filter');
                    });
                } else {
                    $(this).attr('data-view', 'grid');
                    $cardView.fadeOut(150, function() {
                        $listView.fadeIn(150);
                        $listView.removeClass('d-none');
                        $('.white-box').removeClass('no_shadow');
                        $('.listing_filters_wrapper').removeClass('card-filter');
                    });
                }
            });
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }],
                'paging': false // Disable pagination
            });



            var table = $('#myTable').DataTable();

            $(document).on('change', 'input[name="listing_filter"]', function() {
                var filterValue = $(this).val();
                // console.log(filterValue);

                if (filterValue == "0") {
                    // Show all data
                    table.search("").columns().search("").draw();
                } else if (filterValue == "1") {
                    // Filter for Active
                    table.column(7).search("Published").draw();
                } else if (filterValue == "2") {
                    // Filter for Pending
                    table.column(7).search("Pending").draw();
                } else if (filterValue == "3") {
                    // Filter for Accepted
                    table.column(7).search("Accepted").draw();
                } else if (filterValue == "4") {
                    // Filter for In Review
                    table.column(7).search("In Review").draw();
                } else if (filterValue == "5") {
                    // Filter for Drafts
                    table.column(7).search("Draft").draw();
                } else {
                    table.search("").columns().search("").draw();
                }
            });



            $(document).on('change', '.card-filter input', function() {
                var filterValue = $(this).val();
                // console.log("Selected filter: ", filterValue);

                // Show all cards
                if (filterValue == "0") {
                    $('.listing_card_wrapper .listing_card_parent:not(.draft)').show();
                } else if (filterValue == "3") {
                    // Filter for Accepted category
                    $('.listing_card_wrapper .listing_card_parent').each(function() {
                        status = $(this).find('.card-header .status p').data('status').trim();
                        if (status == 'accepted') {
                            $(this).fadeIn();
                        } else {
                            $(this).fadeOut();
                        }
                    });
                } else if (filterValue == "4") {
                    // Filter for In Review category
                    $('.listing_card_wrapper .listing_card_parent').each(function() {
                        status = $(this).find('.card-header .status p').data('status').trim();
                        if (status == 'in_review') {
                            $(this).fadeIn();
                        } else {
                            $(this).fadeOut();
                        }
                    });
                } else if (filterValue == "5") {
                    // Filter for Drafts status
                    $('.listing_card_wrapper .listing_card_parent').each(function() {
                        status = $(this).find('.card-header .status p').data('status').trim();
                        if (status == 'draft') {
                            $(this).fadeIn();
                        } else {
                            $(this).fadeOut();
                        }
                    });
                }
            });


            // reject
            $(".reject-btn").on("click", function() {
                var listing_id = $(this).attr("data-listing-id");
                $("#listing_id").val(listing_id);
            })

            // Suspend 
            // unsuspended listing
            $(".unsuspend_btn").on("click", function() {
                let listing_id = $(this).data("listing-id");
                let type = $(this).data("type");
                Swal.fire({
                    title: "Are you sure?",
                    text: "Do you want to unsuspense this listing!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, unsuspende it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `{{ route('unsuspend_listing', '') }}/${listing_id}`,
                            type: "GET",
                            success: function(res) {
                                if (res.status == true) {
                                    Swal.fire(
                                        'Listing Unsuspended',
                                        `${res.message}`,
                                        'success'
                                    ).then(function() {
                                        location.reload();
                                    })
                                }
                            }
                        });
                    }
                });
            })
            // unsuspended listing end
        })
        $(document).ready(function() {
            function showTabByHash() {
                var currentHash = window.location.hash || '#listing';
                var $targetTab = $('.nav-pills a[href="' + currentHash + '"]');
                if ($targetTab.length > 0) {
                    $('.nav-pills li').removeClass('active');
                    $('.tab-pane').removeClass('active show in');
                    $targetTab.closest('li').addClass('active');
                    $(currentHash).addClass('active show in');
                    sessionStorage.setItem('activeTab', currentHash);
                    setTimeout(function() {
                        sessionStorage.removeItem('activeTab');
                    }, 3000);
                }
                $('html, body').animate({
                    scrollTop: 140
                }, 0);
            }
            var activeTab = sessionStorage.getItem('activeTab');
            if (activeTab) {
                window.location.hash = activeTab;
                showTabByHash();
            } else {
                showTabByHash();
            }
            $(window).on('hashchange', showTabByHash);
            $('.nav-pills a').on('click', function(e) {
                e.preventDefault();
                window.location.hash = this.hash;
                showTabByHash();
            });
            $('form').on('submit', function(e) {
                var currentHash = window.location.hash || '#listing';
                window.location.hash = currentHash;
                showTabByHash();
            });

            $('.del_listing').click(function(e) {
                e.preventDefault();
                let form = $(this).parents('form');
                Swal.fire({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });
        $(".pause_btn").on("click", function() {
            let listing_id = $(this).attr("data-listing-id");
            let pause_type = $(this).attr("data-pause-type");

            if (listing_id) {
                let confirmTitle = pause_type === "pause" ? "Pause" : "Unpause";
                let confirmText = pause_type === "pause" ?
                    "Are you sure you want to pause this listing? It will no longer be visible to users." :
                    "Are you sure you want to make this listing active again?";
                let confirmButtonText = pause_type === "pause" ? "Yes, Pause it!" : "Yes, Unpause it!";

                Swal.fire({
                    title: confirmTitle,
                    text: confirmText,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: confirmButtonText,
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Redirect to the server-side action
                        window.location.href = `{{ route('listing_pause', '') }}/${listing_id}`;
                    }
                });
            }
        });

        @if (!auth()->user()->hasRole('service'))
            // bulk delete
            $(document).ready(function() {
                // Cache selectors
                const checkboxes = $('.user-checkbox');
                const deleteButton = $('#delete-button');
                const checkAll = $('#check-all');
                const confirmDeleteButton = $('#confirm-delete');

                let selectedIds = [];

                // Function to toggle the visibility of the delete button
                function toggleDeleteButton() {
                    selectedIds = checkboxes.filter(':checked').map(function() {
                        return $(this).val();
                    }).get();

                    deleteButton.toggle(selectedIds.length > 0);
                }

                // When individual checkboxes are clicked, update delete button visibility
                checkboxes.on('change', function() {
                    toggleDeleteButton();
                });

                // When "check all" checkbox is clicked, check/uncheck all individual checkboxes
                checkAll.on('change', function() {
                    checkboxes.prop('checked', checkAll.prop('checked'));
                    toggleDeleteButton();
                });

                // Show modal for confirmation before deleting
                deleteButton.on('click', function() {
                    // Before opening the modal, gather selected IDs
                    selectedIds = checkboxes.filter(':checked').map(function() {
                        return $(this).val();
                    }).get();
                    // get listing data
                    $.ajax({
                        url: '{{ route('get_listing_booking') }}', // Adjust to your route
                        method: 'GET',
                        data: {
                            listing_ids: selectedIds
                        },
                        success: function(response) {
                            console.log(response);
                            if (response.status) {
                                $("#confirmationModal .modal-body").empty();
                                $("#confirmationModal .modal-body").append(response.data);
                            } else {
                                $("#confirmationModal .modal-body").empty();
                                $("#confirmationModal .modal-body").append(response.data);
                            }
                        }
                    });
                });

                // Handle confirmation of the delete action
                confirmDeleteButton.on('click', function() {
                    if (selectedIds.length > 0) {
                        // Send an AJAX request to delete the selected users
                        $.ajax({
                            url: '{{ route('listing_bulk_delete') }}', // Adjust to your route
                            method: 'POST',
                            data: {
                                _token: "{{ csrf_token() }}",
                                listing_ids: selectedIds
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Remove rows of deleted users from the table
                                    checkboxes.filter(':checked').each(function() {
                                        $(this).closest('tr').remove();
                                    });
                                    toggleDeleteButton(); // Hide delete button again
                                }
                                $('#confirmationModal').modal('hide'); // Close modal
                            },
                            error: function() {
                                alert('Error deleting users');
                                $('#confirmationModal').modal('hide'); // Close modal
                            }
                        });
                    }
                });
            });
        @endif
        $('.add_listing_disclaimer_modal .agreement_content').scroll(function() {
            // console.log($('.agreement_content')[0].scrollHeight);
            // console.log($(this).scrollTop() + $(this).height() + 1);
            var scrollPosition = Math.floor($(this).scrollTop() + $(this).height() + 1);
            // console.log(scrollPosition);
            if ($('.agreement_content')[0].scrollHeight <= scrollPosition) {
                $('.add_listing_disclaimer_modal .create_btn').removeAttr('disabled');
            }
        });
    </script>
    <script>
        $(".del-active-listing").on("click", function() {
            let listing_id = $(this).data("listing-id");
            //$(".spinner").show();
            $("#del-listing-modal-table").empty();
            $.ajax({
                url: `{{ route('listing_active_booking', '') }}/${listing_id}`,
                type: "GET",
                data: {
                    type: "delete"
                },
                success: function(response) {
                    //$(".spinner").hide();
                    $("#del-listing-modal-table").html(response.data);
                }
            })
        })
        // suspend listing
        $(".suspend_btn").on("click", function() {
            let listing_id = $(this).data("listing-id");
            $(".spinner").show();
            $("#suspense-modal-table").empty();
            $.ajax({
                url: `{{ route('listing_active_booking', '') }}/${listing_id}`,
                type: "GET",
                success: function(response) {
                    $(".spinner").hide();
                    $("#suspense-modal-table").html(response.data);
                }
            })
        })

        // get listing document
        // $(document).on('click', ".get-listing-document", function() {
        //     let listing_id = $(this).data("listing-id");
        //     $(".spinner").show();
        //     $(".uploaded_doc_wrapper").empty();

        //     $.ajax({
        //         url: `{{ route('get_listing_documents', '') }}/${listing_id}`,
        //         type: "GET",
        //         success: function(response) {
        //             $(".spinner").hide();
        //             if (response.status == true) {
        //                 $("#download_all_btn").data("listing-id", listing_id);
        //                 $(".uploaded_doc_wrapper").html(response.data);
        //                 let documentsHtml = '';

        //                 // Loop through the documents and generate HTML
        //                 response.data.forEach(function(doc, index) {
        //                     documentsHtml += `
    //                         <div class="single_doc d-flex justify-content-between align-items-center padding divider">
    //                             <a href="${doc.url}" target="_blank" class="upload_doc_view">
    //                                 <div class="doc d-flex justify-content-between align-items-center gap-3">
    //                                     <!-- Add image if needed -->
    //                                     <img src="{{ asset('website') }}/images/document_icon.png" alt="Doc" class="doc_preview" height="80px" width="100px">
    //                                     <a href="{{ asset('website') }}/${doc.url}" target="_blank" class="doc_title">#${index + 1} ${ doc.name } </a>
    //                                 </div>
    //                             </a>
    //                             <div class="menu_doc">
    //                                 <button class="download trans_btn" type="button" data-file-name="${doc.name}" data-doc-url="{{ asset('website') }}/${doc.url}">
    //                                     <i class="fas fa-download fs-20" style="color: var(--primary-color);"></i>
    //                                 </button>
    //                             </div>
    //                         </div>
    //                     `;
        //                 });
        //                 // Insert the generated HTML into the wrapper
        //                 $(".uploaded_doc_wrapper").html(documentsHtml);
        //             }
        //         }
        //     })
        // })


        $(document).on('click', ".get-listing-document", function() {
            let listing_id = $(this).data("listing-id");
            $(".spinner").show();
            $(".uploaded_doc_wrapper").empty();

            $.ajax({
                url: `{{ route('get_listing_documents', '') }}/${listing_id}`,
                type: "GET",
                success: function(response) {
                    $(".spinner").hide();
                    if (response.status == true) {
                        $("#download_all_btn").data("listing-id", listing_id);
                        let documentsHtml = '';

                        response.data.forEach(function(doc, index) {
                            let previewHtml = '';
                            let fileExtension = doc.url.split('.').pop()
                                .toLowerCase(); // Extract file extension

                            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(
                                    fileExtension)) {
                                // Image preview
                                previewHtml =
                                    `<img src="{{ asset('website') }}/${doc.url}" alt="Image Preview" class="doc_preview" height="80px" width="100px">`;
                            } else if (fileExtension === 'pdf') {
                                // PDF preview (First page using PDF.js)
                                previewHtml =
                                    `<canvas class="pdf-preview" data-pdf-url="{{ asset('website') }}/${doc.url}" width="100"></canvas>`;
                            } else {
                                // Default document icon for other file types
                                previewHtml =
                                    `<img src="{{ asset('website') }}/images/document_icon.png" alt="Doc" class="doc_preview" height="80px" width="100px">`;
                            }

                            documentsHtml += `
                                <div class="single_doc">
                                    <a href="#" class="upload_doc_view" data-document-url="{{ asset('website') }}/${doc.url}">
                                        <div class="doc">
                                            <div class="image_wrapper">
                                                ${previewHtml}
                                                <div class="menu_doc">
                                                    <button class="download trans_btn" type="button" data-file-name="${doc.name}" data-doc-url="{{ asset('website') }}/${doc.url}">
                                                        <i class="fas fa-download fs-20" style="color: var(--primary-color);"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <span class="doc_title">#${index + 1} ${ doc.name }</span>
                                        </div>
                                    </a>
                                    
                                </div>
                            `;
                        });


                        $(".uploaded_doc_wrapper").html(documentsHtml);

                        // Load PDF previews
                        $(".pdf-preview").each(function() {
                            let canvas = $(this)[0];
                            let pdfUrl = $(this).data("pdf-url");
                            renderPdfPreview(pdfUrl, canvas);
                        });
                    }
                }
            });
        });

        // Function to render PDF first page preview
        function renderPdfPreview(pdfUrl, canvas) {
            var loadingTask = pdfjsLib.getDocument(pdfUrl);
            loadingTask.promise.then(function(pdf) {
                return pdf.getPage(1);
            }).then(function(page) {
                var scale = 0.5;
                var viewport = page.getViewport({
                    scale: scale
                });
                var context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                var renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                page.render(renderContext);
            });
        }



        // get listing document end 
        // Handle the download button click
        $(document).on('click', '.download', function() {
            var docUrl = $(this).data('doc-url');
            var customFilename = $(this).data("file-name");

            var a = document.createElement('a');
            a.href = docUrl;
            a.download = customFilename;

            // Append to the body for Safari compatibility
            document.body.appendChild(a);

            // Use a timeout to trigger the click event
            setTimeout(function() {
                a.click();
                document.body.removeChild(a);
            }, 100); // 100ms delay
        });
        // Handle the view button click to open document in a new tab
        $(document).on('click', '.view-document', function() {
            var docUrl = $(this).data('doc-url');
            window.open(docUrl, '_blank'); // Open the document in a new tab
        });
        $(document).on('click', '#download_all_btn', function() {
            const listingId = $(this).data("listing-id"); // Replace dynamically
            const url = `{{ route('download_listing_assets_zip', '') }}/${listingId}`;

            // Create a hidden iframe to trigger download
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = url;
            document.body.appendChild(iframe);

            // Optional: Remove iframe after download starts
            setTimeout(() => document.body.removeChild(iframe), 5000);
        });
        $(document).on("click", ".internal-name-model-btn", function() {
            let listing_id = $(this).data("listing-id")
            let internal_name = $(this).data("internal-name")
            $("#listing_ids_internal").val(listing_id)
            $("#internal_name_listing").val(internal_name)
        })
        $(document).on('click', ".copy-link", function() {
            // Get the value of the data-link attribute
            // var link = $(this).data('link');

            // Create a temporary input element to hold the link value
            // var $tempInput = $('<input>');
            // $('body').append($tempInput);
            // $tempInput.val(link).select();
            // document.execCommand('copy');
            // $tempInput.remove();

            var listingId = $(this).data('listing-id');
            $.ajax({
                url: `{{ route('listing.generate_link', '') }}/${listingId}`,
                type: "GET",
                success: function(response) {
                    if (response.status) {
                        const listingUrl = response.data;
                        const tempInput = document.createElement('input');
                        tempInput.value = listingUrl;
                        document.body.appendChild(tempInput);
                        tempInput.select();
                        document.execCommand('copy');
                        document.body.removeChild(tempInput);
                        Swal.fire({
                            title: "Copied",
                            text: "Link copied to clipboard",
                            icon: "success"
                        });
                    } else {
                        Swal.fire({
                            title: "Error",
                            text: "Failed to generate listing URL",
                            icon: "error"
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: "Error",
                        text: "Error occurred while generating the listing URL",
                        icon: "error"
                    });
                }
            });
        });


        @if (auth()->user()->hasRole('service'))

            $(document).on('change', '[name="listing_filter"]', function() {
                var selectedValue = $('[name="listing_filter"]:checked').val();
                // console.log(selectedValue);
                if (selectedValue == 0) {
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr, .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent')
                        .show();
                } else if (selectedValue == 3) {
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr, .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent')
                        .hide();
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr[data-status*="accepted"], .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent.accepted')
                        .show();
                } else if (selectedValue == 4) {
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr, .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent')
                        .hide();
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr[data-status*="in_review"], .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent.in_review')
                        .show();
                } else if (selectedValue == 5) {
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr, .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent')
                        .hide();
                    $('.lisiting_table .listing_parent_wrapper .list_view table tbody tr[data-status*="draft"], .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent.draft')
                        .show();
                }

                var visibleItems = $(
                    '.lisiting_table .listing_parent_wrapper .list_view table tbody tr:visible, .lisiting_table .listing_parent_wrapper .card_view .listing_card_parent:visible'
                );
                var errorMessage = $('.lisiting_table .listing_parent_wrapper .white-box .not-found-message');

                if (visibleItems.length === 0) {
                    $('.lisiting_table .listing_parent_wrapper .white-box').append(
                        '<h3 class="not-found-message text-center poppins-medium"><span class="not-found-text">No listing found</span></h3>'
                    );
                } else {
                    $('.lisiting_table .listing_parent_wrapper .white-box .not-found-message').remove();
                }

                if (selectedValue == 0) {
                    $('.not-found-text').text('No listing yet');
                } else if (selectedValue == 3) {
                    $('.not-found-text').text('No listings currently published');
                } else if (selectedValue == 4) {
                    $('.not-found-text').text('No listings in review yet');
                } else if (selectedValue == 5) {
                    $('.not-found-text').text('No listings in draft yet');
                }
            });


            // Bulk Delete
            // Cache selectors
            const checkboxes = $('.user-checkbox');
            const deleteButton = $('#delete-button');
            const checkAll = $('#check-all');
            const confirmDeleteButton = $('#confirm-delete');

            let selectedIds = [];

            // Function to toggle the visibility of the delete button
            function toggleDeleteButton() {
                selectedIds = checkboxes.filter(':checked').map(function() {
                    return $(this).val();
                }).get();

                deleteButton.toggle(selectedIds.length > 0);
            }

            // When individual checkboxes are clicked, update delete button visibility
            checkboxes.on('change', function() {
                toggleDeleteButton();
            });

            // When "check all" checkbox is clicked, check/uncheck all individual checkboxes
            checkAll.on('change', function() {
                checkboxes.prop('checked', checkAll.prop('checked'));
                toggleDeleteButton();
            });

            // Show modal for confirmation before deleting
            deleteButton.on('click', function() {
                // Before opening the modal, gather selected IDs
                selectedIds = checkboxes.filter(':checked').map(function() {
                    return $(this).val();
                }).get();
                // get listing data
                $.ajax({
                    url: '{{ route('get_listing_booking') }}', // Adjust to your route
                    method: 'GET',
                    data: {
                        listing_ids: selectedIds
                    },
                    success: function(response) {
                        console.log(response);
                        if (response.status) {
                            $("#confirmationModal .modal-body").empty();
                            $("#confirmationModal .modal-body").append(response.data);
                        } else {
                            $("#confirmationModal .modal-body").empty();
                            $("#confirmationModal .modal-body").append(response.data);
                        }
                    }
                });
            });

            // Handle confirmation of the delete action
            confirmDeleteButton.on('click', function() {
                if (selectedIds.length > 0) {
                    // Send an AJAX request to delete the selected users
                    $.ajax({
                        url: '{{ route('listing_bulk_delete') }}', // Adjust to your route
                        method: 'POST',
                        data: {
                            _token: "{{ csrf_token() }}",
                            listing_ids: selectedIds
                        },
                        success: function(response) {
                            if (response.success) {
                                // Remove rows of deleted users from the table
                                checkboxes.filter(':checked').each(function() {
                                    $(this).closest('tr').remove();
                                });
                                toggleDeleteButton(); // Hide delete button again
                            }
                            $('#confirmationModal').modal('hide'); // Close modal
                        },
                        error: function() {
                            alert('Error deleting users');
                            $('#confirmationModal').modal('hide'); // Close modal
                        }
                    });
                }
            });

            $('#searchBar').keyup(function() {
                var searchValue = $(this).val().toLowerCase();

                $('.card_view .listing_card_parent').each(function() {
                    var cardTitle = $(this).find('.title').text().toLowerCase();

                    if (cardTitle.includes(searchValue)) {
                        $(this).show();
                        $('.not_found').hide();
                    } else {
                        $(this).hide();
                        $('.not_found').show();
                    }
                });
            });
        @endif
    </script>
    @if (session()->has('consent'))
        <script>
            $(function() {
                $('#add_listing_disclaimer_modal').modal('show');
            });
        </script>
    @endif
@endpush
